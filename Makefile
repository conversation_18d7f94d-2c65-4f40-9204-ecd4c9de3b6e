# Makefile for APA .dat文件回放工具

CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -O2
LDFLAGS = -pthread

# 目标程序名
TARGET = pc_dat_sender

# 源文件
SOURCES = pc_dat_sender.cpp

# 目标文件
OBJECTS = $(SOURCES:.cpp=.o)

# 默认目标
all: $(TARGET)

# 编译目标程序
$(TARGET): $(OBJECTS)
	$(CXX) $(OBJECTS) -o $(TARGET) $(LDFLAGS)
	@echo "Build completed: $(TARGET)"
	@echo "Usage: ./$(TARGET) <dat_files_directory> [options]"

# 编译源文件
%.o: %.cpp
	$(CXX) $(CXXFLAGS) -c $< -o $@

# 清理
clean:
	rm -f $(OBJECTS) $(TARGET)
	@echo "Clean completed"

# 安装到系统路径
install: $(TARGET)
	sudo cp $(TARGET) /usr/local/bin/
	@echo "Installed $(TARGET) to /usr/local/bin/"

# 从系统路径卸载
uninstall:
	sudo rm -f /usr/local/bin/$(TARGET)
	@echo "Uninstalled $(TARGET) from /usr/local/bin/"

# 创建发布包
package: $(TARGET)
	mkdir -p release
	cp $(TARGET) release/
	cp README.md release/ 2>/dev/null || echo "# APA .dat File Playback Tool" > release/README.md
	tar -czf apa_dat_playback_tool.tar.gz release/
	@echo "Package created: apa_dat_playback_tool.tar.gz"

# 运行示例
run-example:
	@echo "Usage examples:"
	@echo "  ./$(TARGET) /path/to/dat/files"
	@echo "  ./$(TARGET) /path/to/dat/files -p 5010 -s 1.5 -l"
	@echo ""
	@echo "Options:"
	@echo "  -p <port>     Server port (default: 5010)"
	@echo "  -s <speed>    Playback speed (default: 1.0, range: 0.1-10.0)"
	@echo "  -l            Enable loop playback"
	@echo "  -h            Show help"

# 测试编译
test-compile: $(TARGET)
	@echo "Compilation test passed"

.PHONY: all clean install uninstall package run-example test-compile
