# APA .dat文件回放工具

![Version](https://img.shields.io/badge/version-2.0-blue.svg)
![Platform](https://img.shields.io/badge/platform-Linux-green.svg)
![C++](https://img.shields.io/badge/C++-17-blue.svg)

## 📖 概述

APA .dat文件回放工具是一个高性能的数据回放系统，用于将PC端保存的.dat文件通过网络发送给板端进行算法调试、测试和验证。该工具支持实时帧率监控、可调节播放速度、暂停/恢复控制等高级功能。

### ✨ 主要特性

- 🚀 **高性能传输**: 基于TCP Socket的高效数据传输
- 📊 **实时监控**: 实时FPS显示和详细统计信息
- ⚡ **速率控制**: 0.1x - 10.0x可调节播放速度
- 🎮 **运行时控制**: 支持暂停/恢复、循环播放等控制命令
- 🔧 **多线程架构**: 独立的控制线程和统计线程
- 🛡️ **线程安全**: 使用原子操作和互斥锁保证数据安全
- 🔄 **自动重连**: 网络断开时自动重连机制

## 🏗️ 系统架构

```
┌─────────────────────────────────┐    TCP Socket    ┌──────────────────────────────────┐
│           PC端 (客户端)            │   ************   │            板端 (服务器)            │
│                                 │     :5020        │                                  │
│  ┌─────────────────────────────┐ │                  │  ┌─────────────────────────────┐ │
│  │      pc_dat_sender         │ │ ◄──────────────► │  │      DatInputModule        │ │
│  │                            │ │                  │  │                            │ │
│  │ • .dat文件加载             │ │                  │  │ • TCP服务器                │ │
│  │ • TCP客户端                │ │                  │  │ • Protocol Buffers解析     │ │
│  │ • 播放速率控制             │ │                  │  │ • 图像解码                  │ │
│  │ • 实时FPS监控              │ │                  │  │ • PyramidFrame转换          │ │
│  │ • 控制命令界面             │ │                  │  │ • APA算法处理               │ │
│  └─────────────────────────────┘ │                  │  └─────────────────────────────┘ │
└─────────────────────────────────┘                  └──────────────────────────────────┘
```

## 🚀 快速开始

### 1️⃣ 编译PC端工具

```bash
cd /path/to/dat_playback
g++ -std=c++17 -pthread -O2 -o pc_dat_sender pc_dat_sender.cpp
```

### 2️⃣ 准备数据文件

确保您有.dat文件目录：
```
/data/dat_files/
├── frame_001.dat
├── frame_002.dat
├── frame_003.dat
└── ...
```

### 3️⃣ 启动回放

```bash
# 基本用法
./pc_dat_sender /data/dat_files

# 高级用法
./pc_dat_sender /data/dat_files -s 1.5 -l
```

## 📋 使用说明

### 命令行参数

```bash
./pc_dat_sender <dat_files_directory> [options]
```

| 参数 | 说明 | 默认值 | 范围 |
|------|------|--------|------|
| `<directory>` | .dat文件目录路径 | - | 必需 |
| `-s <speed>` | 播放速度倍率 | 1.0 | 0.1 - 10.0 |
| `-l` | 启用循环播放 | 关闭 | - |
| `-h` | 显示帮助信息 | - | - |

### 🎮 运行时控制命令

程序运行时支持以下交互命令：

| 命令 | 功能 | 示例 |
|------|------|------|
| `speed <value>` | 设置播放速度 | `speed 2.0` |
| `loop` | 切换循环播放模式 | `loop` |
| `pause` | 暂停/恢复播放 | `pause` |
| `stats` | 显示详细统计信息 | `stats` |
| `help` | 显示命令帮助 | `help` |
| `quit` | 退出程序 | `quit` |

### 📊 实时监控显示

程序运行时会显示实时状态信息：

```
[FPS: 10.2 | Total: 1547 | Speed: 1.0x]
```

- **FPS**: 当前发送帧率
- **Total**: 已发送文件总数
- **Speed**: 当前播放速度

## ⚙️ 配置说明

### 发送参数配置

| 参数 | 说明 | 默认值 |
|------|------|--------|
| 板端IP | 目标板端IP地址 | ************ |
| 板端端口 | 目标板端端口 | 5020 |
| 发送间隔 | 基础发送间隔 | 100ms (10 FPS) |
| 缓冲区大小 | TCP发送缓冲区 | 1MB |

### 播放速度对照表

| 播放速度 | 发送间隔 | 理论FPS | 适用场景 |
|----------|----------|---------|----------|
| 0.1x | 1000ms | 1.0 FPS | 慢速调试 |
| 0.5x | 200ms | 5.0 FPS | 详细分析 |
| **1.0x** | **100ms** | **10.0 FPS** | **正常播放** |
| 2.0x | 50ms | 20.0 FPS | 快速测试 |
| 5.0x | 20ms | 50.0 FPS | 批量处理 |
| 10.0x | 10ms | 100.0 FPS | 极速处理 |

## 🔗 通信协议

### TCP通信流程

```mermaid
sequenceDiagram
    participant PC as PC端客户端
    participant Board as 板端服务器
    
    PC->>Board: TCP连接请求
    Board->>PC: 连接确认
    
    loop 每个.dat文件
        PC->>Board: 文件信息 (filename\nfilesize)
        Board->>PC: ACK确认
        PC->>Board: 文件数据 (分块传输)
        Board->>PC: ACK1完成确认
    end
    
    PC->>Board: 断开连接
```

### 数据传输格式

1. **文件信息阶段**
   ```
   filename.dat\n123456
   ```

2. **数据传输阶段**
   - 使用1MB缓冲区分块传输
   - 支持大文件的流式传输

## 📈 性能监控

### 统计信息详情

使用 `stats` 命令查看详细统计：

```
=== Statistics ===
Total files sent: 1547
Total bytes sent: 2.34 GB
Average FPS: 9.8
Current FPS: 10.2
Current file: 156/500
Current file name: frame_156.dat
Playback speed: 1.0x
=================
```

## 🛠️ 故障排除

### 常见问题及解决方案

#### 1. 连接失败
```bash
Failed to connect to board, retrying in 3 seconds...
```
**解决方案**:
- 检查板端是否启动TCP服务器
- 验证IP地址和端口配置
- 检查网络连通性: `ping ************`
- 检查端口占用: `netstat -an | grep 5020`

#### 2. 编译错误
```bash
fatal error: cannot execute 'cc1plus'
```
**解决方案**:
```bash
# Ubuntu/Debian
sudo apt update && sudo apt install build-essential

# CentOS/RHEL
sudo yum groupinstall "Development Tools"
```

#### 3. 文件加载失败
```bash
No .dat files found in directory
```
**解决方案**:
- 确认目录路径正确
- 检查文件扩展名为 `.dat`
- 验证文件读取权限

#### 4. 发送失败
```bash
Send failed, client may have disconnected
```
**解决方案**:
- 检查网络连接稳定性
- 确认板端正常接收数据
- 检查防火墙设置

### 🔍 调试模式

#### 启用详细日志
修改代码中的日志级别或使用调试编译：
```bash
g++ -std=c++17 -pthread -g -DDEBUG -o pc_dat_sender pc_dat_sender.cpp
```

#### 网络监控
```bash
# 监控TCP连接
watch -n 1 'netstat -an | grep 5020'

# 监控网络流量
sudo iftop -i eth0 -f "port 5020"
```

## 🔧 高级配置

### 源码自定义

如需修改核心参数，可编辑源码：

```cpp
// 修改缓冲区大小
#define BUFF_SIZE (2 * 1024 * 1024)  // 改为2MB

// 修改默认连接参数
DatFileSender sender("*************", 8080);  // 自定义IP和端口

// 修改默认发送间隔
auto target_interval = std::chrono::milliseconds(50);  // 改为50ms间隔
```

### 编译优化

```bash
# 性能优化编译
g++ -std=c++17 -pthread -O3 -march=native -DNDEBUG -o pc_dat_sender pc_dat_sender.cpp

# 调试版本编译
g++ -std=c++17 -pthread -g -O0 -DDEBUG -o pc_dat_sender_debug pc_dat_sender.cpp
```

## 📝 开发指南

### 代码结构

```
pc_dat_sender.cpp
├── Statistics结构体         # 统计信息管理
├── DatFileSender类          # 主要功能类
│   ├── 网络管理              # TCP连接和通信
│   ├── 文件管理              # .dat文件加载和发送
│   ├── 播放控制              # 速度、暂停、循环控制
│   ├── 统计监控              # FPS计算和显示
│   └── 用户界面              # 命令行交互
└── main函数                 # 程序入口和参数解析
```

