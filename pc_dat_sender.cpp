#include <iostream>
#include <fstream>
#include <filesystem>
#include <vector>
#include <string>
#include <thread>
#include <chrono>
#include <algorithm>
#include <atomic>
#include <mutex>
#include <iomanip>
#include <sstream>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <cstring>
#include <termios.h>
#include <fcntl.h>

#define BUFF_SIZE (1024 * 1024)

/**
 * @brief PC端.dat文件发送程序
 * 位置: tools/dat_playback/pc_dat_sender.cpp
 * 
 * 功能: 读取保存的.dat文件，通过网络发送给板端进行回放
 * 用法: ./pc_dat_sender <dat_files_directory> [options]
 * 
 * 特性:
 * - 支持可调节的发送速率
 * - 实时帧率统计和显示
 * - 循环播放模式
 * - 运行时控制命令
 */

/**
 * @brief 统计信息结构体
 */
struct Statistics {
    std::atomic<uint64_t> total_files_sent{0};
    std::atomic<uint64_t> total_bytes_sent{0};
    std::atomic<uint64_t> files_sent_in_period{0};
    std::chrono::steady_clock::time_point start_time;
    std::chrono::steady_clock::time_point last_reset_time;
    
    Statistics() {
        reset();
    }
    
    void reset() {
        start_time = std::chrono::steady_clock::now();
        last_reset_time = start_time;
        files_sent_in_period = 0;
    }
    
    double get_total_fps() const {
        auto now = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - start_time).count();
        if (duration == 0) return 0.0;
        return (double)total_files_sent * 1000.0 / duration;
    }
    
    double get_current_fps() const {
        auto now = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - last_reset_time).count();
        if (duration == 0) return 0.0;
        return (double)files_sent_in_period * 1000.0 / duration;
    }
    
    void reset_current_period() {
        last_reset_time = std::chrono::steady_clock::now();
        files_sent_in_period = 0;
    }
};

/**
 * @brief .dat文件发送器类
 */
class DatFileSender {
private:
    // 网络配置
    std::string board_ip_;
    int board_port_;
    
    // 播放控制
    std::atomic<float> playback_speed_;
    std::atomic<bool> loop_playback_;
    std::atomic<bool> is_running_;
    std::atomic<bool> is_paused_;
    
    // 文件管理
    std::vector<std::string> dat_files_;
    std::atomic<size_t> current_file_index_;
    
    // 统计信息
    Statistics stats_;
    mutable std::mutex stats_mutex_;
    
    // 控制线程
    std::thread control_thread_;
    std::thread stats_thread_;

public:
    /**
     * @brief 构造函数
     * @param ip 板端IP地址
     * @param port 板端端口
     */
    explicit DatFileSender(const std::string& ip = "************", int port = 5020)
        : board_ip_(ip), board_port_(port), playback_speed_(0.5f),
          loop_playback_(false), is_running_(false), is_paused_(false),
          current_file_index_(0) {}
    
    /**
     * @brief 析构函数
     */
    ~DatFileSender() {
        stop_sender();
        if (control_thread_.joinable()) {
            control_thread_.join();
        }
        if (stats_thread_.joinable()) {
            stats_thread_.join();
        }
    }
    
    /**
     * @brief 加载目录中的所有.dat文件
     * @param directory 文件目录路径
     * @return 成功返回true，失败返回false
     */
    bool load_dat_files(const std::string& directory) {
        dat_files_.clear();
        
        try {
            for (const auto& entry : std::filesystem::directory_iterator(directory)) {
                if (entry.path().extension() == ".dat") {
                    dat_files_.push_back(entry.path().string());
                }
            }
        } catch (const std::exception& e) {
            std::cerr << "Error reading directory: " << e.what() << std::endl;
            return false;
        }
        
        if (dat_files_.empty()) {
            std::cerr << "No .dat files found in directory: " << directory << std::endl;
            return false;
        }
        
        // 按文件名排序，确保按时间顺序发送
        std::sort(dat_files_.begin(), dat_files_.end());
        
        std::cout << "从目录加载了 " << dat_files_.size() << " 个.dat文件: " << directory << std::endl;
        for (size_t i = 0; i < dat_files_.size(); ++i) {
            std::cout << "  [" << std::setw(3) << i + 1 << "] " 
                     << std::filesystem::path(dat_files_[i]).filename() << std::endl;
        }
        
        return true;
    }
    
    /**
     * @brief 设置播放速度
     * @param speed 播放速度倍率 (0.1 - 1.0)
     */
    void set_playback_speed(float speed) {
        if (speed >= 0.1f && speed <= 1.0f) {
            playback_speed_ = speed;
            std::cout << "播放速度设置为: " << std::fixed << std::setprecision(1) << speed << "x" << std::endl;
        } else {
            std::cout << "无效速度。范围: 0.1 - 1.0" << std::endl;
        }
    }

    /**
     * @brief 增加播放速度
     */
    void increase_speed() {
        float current_speed = playback_speed_.load();
        float new_speed = std::min(1.0f, current_speed + 0.1f);
        if (new_speed != current_speed) {
            playback_speed_ = new_speed;
            std::cout << "\r播放速度: " << std::fixed << std::setprecision(1) << new_speed << "x" << std::flush;
        }
    }

    /**
     * @brief 减少播放速度
     */
    void decrease_speed() {
        float current_speed = playback_speed_.load();
        float new_speed = std::max(0.1f, current_speed - 0.1f);
        if (new_speed != current_speed) {
            playback_speed_ = new_speed;
            std::cout << "\r播放速度: " << std::fixed << std::setprecision(1) << new_speed << "x" << std::flush;
        }
    }
    
    /**
     * @brief 设置循环播放模式
     * @param loop 是否启用循环播放
     */
    void set_loop_mode(bool loop) {
        loop_playback_ = loop;
        std::cout << "循环播放: " << (loop ? "开启" : "关闭") << std::endl;
    }
    
    /**
     * @brief 暂停/恢复播放
     */
    void toggle_pause() {
        is_paused_ = !is_paused_;
        std::cout << "播放 " << (is_paused_ ? "暂停" : "恢复") << std::endl;
    }
    
    /**
     * @brief 获取当前统计信息
     */
    void print_statistics() const {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        std::cout << "\n=== 统计信息 ===" << std::endl;
        std::cout << "已发送文件总数: " << stats_.total_files_sent << std::endl;
        std::cout << "已发送字节总数: " << format_bytes(stats_.total_bytes_sent) << std::endl;
        std::cout << "平均帧率: " << std::fixed << std::setprecision(2) << stats_.get_total_fps() << " FPS" << std::endl;
        std::cout << "当前帧率: " << std::fixed << std::setprecision(2) << stats_.get_current_fps() << " FPS" << std::endl;
        std::cout << "当前文件: " << (current_file_index_ + 1) << "/" << dat_files_.size() << std::endl;
        if (!dat_files_.empty() && current_file_index_ < dat_files_.size()) {
            std::cout << "当前文件名: " << std::filesystem::path(dat_files_[current_file_index_]).filename() << std::endl;
        }
        std::cout << "播放速度: " << playback_speed_.load() << "x" << std::endl;
        std::cout << "=================" << std::endl;
    }
    
    /**
     * @brief 启动发送器
     * @return 成功返回true，失败返回false
     */
    bool start_sending() {
        if (dat_files_.empty()) {
            std::cerr << "No .dat files loaded. Call load_dat_files() first." << std::endl;
            return false;
        }

        is_running_ = true;
        stats_.reset();
        
        // 启动统计线程
        start_statistics_thread();

        while (is_running_) {
            // 连接到板端
            int client_fd = connect_to_board();
            if (client_fd < 0) {
                std::cerr << "Failed to connect to board, retrying in 3 seconds..." << std::endl;
                std::this_thread::sleep_for(std::chrono::seconds(3));
                continue;
            }

            std::cout << "Connected to board: " << board_ip_ << ":" << board_port_ << std::endl;

            // 发送数据
            handle_client(client_fd);

            close(client_fd);
            std::cout << "Disconnected from board" << std::endl;

            // 如果不是循环播放，退出
            if (!loop_playback_) {
                break;
            }

            std::cout << "Waiting 3 seconds before reconnecting..." << std::endl;
            std::this_thread::sleep_for(std::chrono::seconds(3));
        }

        return true;
    }
    
    /**
     * @brief 停止发送器
     */
    void stop_sender() {
        is_running_ = false;
    }
    
    /**
     * @brief 启动控制线程
     */
    void start_control_thread() {
        control_thread_ = std::thread([this]() {
            this->control_loop();
        });
    }
    
private:
    /**
     * @brief 设置终端为非阻塞模式
     */
    void set_nonblocking_input() {
        struct termios ttystate;
        tcgetattr(STDIN_FILENO, &ttystate);
        ttystate.c_lflag &= ~(ICANON | ECHO);
        ttystate.c_cc[VMIN] = 0;
        ttystate.c_cc[VTIME] = 0;
        tcsetattr(STDIN_FILENO, TCSANOW, &ttystate);

        int flags = fcntl(STDIN_FILENO, F_GETFL, 0);
        fcntl(STDIN_FILENO, F_SETFL, flags | O_NONBLOCK);
    }

    /**
     * @brief 恢复终端正常模式
     */
    void restore_terminal() {
        struct termios ttystate;
        tcgetattr(STDIN_FILENO, &ttystate);
        ttystate.c_lflag |= (ICANON | ECHO);
        tcsetattr(STDIN_FILENO, TCSANOW, &ttystate);

        int flags = fcntl(STDIN_FILENO, F_GETFL, 0);
        fcntl(STDIN_FILENO, F_SETFL, flags & ~O_NONBLOCK);
    }

    /**
     * @brief 检查键盘输入
     * @return 返回按键码，无输入返回0
     */
    int check_keyboard_input() {
        char ch;
        if (read(STDIN_FILENO, &ch, 1) == 1) {
            if (ch == 27) { // ESC序列开始
                char seq[3];
                if (read(STDIN_FILENO, &seq[0], 1) == 1 && seq[0] == '[') {
                    if (read(STDIN_FILENO, &seq[1], 1) == 1) {
                        switch (seq[1]) {
                            case 'A': return 1; // 上箭头
                            case 'B': return 2; // 下箭头
                            case 'C': return 3; // 右箭头
                            case 'D': return 4; // 左箭头
                        }
                    }
                }
            } else {
                return ch; // 普通字符
            }
        }
        return 0; // 无输入
    }

    /**
     * @brief 连接到板端
     * @return 成功返回socket描述符，失败返回-1
     */
    int connect_to_board() {
        int client_fd = socket(AF_INET, SOCK_STREAM, 0);
        if (client_fd == -1) {
            std::cerr << "Socket creation failed: " << strerror(errno) << std::endl;
            return -1;
        }

        // 设置socket选项
        int val = 1;
        setsockopt(client_fd, SOL_SOCKET, SO_REUSEADDR, &val, sizeof(val));

        struct sockaddr_in board_addr;
        board_addr.sin_family = AF_INET;
        board_addr.sin_port = htons(board_port_);

        if (inet_pton(AF_INET, board_ip_.c_str(), &board_addr.sin_addr) <= 0) {
            std::cerr << "Invalid board address: " << board_ip_ << std::endl;
            close(client_fd);
            return -1;
        }

        if (connect(client_fd, (struct sockaddr*)&board_addr, sizeof(board_addr)) < 0) {
            std::cerr << "Connect to board failed: " << strerror(errno) << std::endl;
            close(client_fd);
            return -1;
        }

        return client_fd;
    }

    /**
     * @brief 处理客户端连接，发送文件数据
     * @param client_fd 客户端socket描述符
     */
    void handle_client(int client_fd) {
        do {
            std::cout << "开始播放..." << std::endl;
            
            for (current_file_index_ = 0; current_file_index_ < dat_files_.size() && is_running_; ++current_file_index_) {
                // 检查暂停状态
                while (is_paused_ && is_running_) {
                    std::this_thread::sleep_for(std::chrono::milliseconds(100));
                }
                
                if (!is_running_) break;
                
                const auto& dat_file = dat_files_[current_file_index_];
                
                auto start_time = std::chrono::steady_clock::now();
                
                if (!send_dat_file(client_fd, dat_file)) {
                    std::cout << "Send failed, client may have disconnected" << std::endl;
                    return;
                }
                
                // 更新统计信息
                {
                    std::lock_guard<std::mutex> lock(stats_mutex_);
                    stats_.total_files_sent++;
                    stats_.files_sent_in_period++;
                }
                
                // 控制发送速度
                auto elapsed = std::chrono::steady_clock::now() - start_time;
                auto target_interval = std::chrono::milliseconds(static_cast<int>(100 / playback_speed_.load()));
                
                if (elapsed < target_interval) {
                    std::this_thread::sleep_for(target_interval - elapsed);
                }
            }
            
            if (loop_playback_ && is_running_) {
                std::cout << "循环播放，重新开始..." << std::endl;
            }
            
        } while (loop_playback_ && is_running_);
        
        std::cout << "播放完成" << std::endl;
    }
    
    /**
     * @brief 发送单个.dat文件
     * @param client_fd 客户端socket描述符
     * @param filepath 文件路径
     * @return 成功返回true，失败返回false
     */
    bool send_dat_file(int client_fd, const std::string& filepath) {
        // 1. 读取.dat文件
        std::ifstream file(filepath, std::ios::binary);
        if (!file.is_open()) {
            std::cerr << "Failed to open file: " << filepath << std::endl;
            return false;
        }
        
        // 获取文件大小
        file.seekg(0, std::ios::end);
        size_t filesize = file.tellg();
        file.seekg(0, std::ios::beg);
        
        // 读取文件数据
        std::vector<char> file_data(filesize);
        file.read(file_data.data(), filesize);
        file.close();
        
        // 2. 发送文件信息 (filename + '\n' + filesize)
        std::string filename = std::filesystem::path(filepath).filename();
        std::string file_info = filename + "\n" + std::to_string(filesize);
        
        if (send(client_fd, file_info.c_str(), file_info.length(), 0) <= 0) {
            std::cerr << "Failed to send file info" << std::endl;
            return false;
        }
        
        // 3. 等待ACK确认
        if (!wait_for_ack(client_fd, "ACK")) {
            std::cerr << "Failed to receive ACK" << std::endl;
            return false;
        }
        
        // 4. 发送文件数据
        size_t total_sent = 0;
        while (total_sent < filesize) {
            size_t chunk_size = std::min(static_cast<size_t>(BUFF_SIZE), filesize - total_sent);
            int sent = send(client_fd, file_data.data() + total_sent, chunk_size, 0);
            
            if (sent <= 0) {
                std::cerr << "Failed to send file data" << std::endl;
                return false;
            }
            
            total_sent += sent;
        }
        
        // 更新字节统计
        {
            std::lock_guard<std::mutex> lock(stats_mutex_);
            stats_.total_bytes_sent += filesize;
        }
        
        // 5. 等待ACK1确认
        if (!wait_for_ack(client_fd, "ACK1")) {
            std::cerr << "Failed to receive ACK1" << std::endl;
            return false;
        }
        
        return true;
    }
    
    /**
     * @brief 等待ACK确认
     * @param client_fd 客户端socket描述符
     * @param expected_ack 期望的ACK字符串
     * @return 成功返回true，失败返回false
     */
    bool wait_for_ack(int client_fd, const std::string& expected_ack) {
        char ack_buffer[10] = {0};
        int received = recv(client_fd, ack_buffer, sizeof(ack_buffer) - 1, 0);
        
        if (received <= 0) {
            return false;
        }
        
        ack_buffer[received] = '\0';
        return std::string(ack_buffer) == expected_ack;
    }
    
    /**
     * @brief 控制命令循环
     */
    void control_loop() {
        set_nonblocking_input();
        print_help();

        while (is_running_) {
            int key = check_keyboard_input();

            if (key == 1) { // 上箭头 - 增加速度
                increase_speed();
            } else if (key == 2) { // 下箭头 - 减少速度
                decrease_speed();
            } else if (key == 'q' || key == 'Q') { // Q键退出
                std::cout << "\n退出程序..." << std::endl;
                stop_sender();
                break;
            } else if (key == 'p' || key == 'P') { // P键暂停/恢复
                toggle_pause();
            } else if (key == 'l' || key == 'L') { // L键切换循环
                set_loop_mode(!loop_playback_.load());
            } else if (key == 's' || key == 'S') { // S键显示统计
                print_statistics();
            } else if (key == 'h' || key == 'H') { // H键显示帮助
                print_help();
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(50));
        }

        restore_terminal();
    }
    
    /**
     * @brief 启动统计线程
     */
    void start_statistics_thread() {
        stats_thread_ = std::thread([this]() {
            while (is_running_) {
                std::this_thread::sleep_for(std::chrono::seconds(5));
                if (is_running_) {
                    std::cout << "\r[帧率: " << std::fixed << std::setprecision(1)
                             << stats_.get_current_fps() << " | 总计: "
                             << stats_.total_files_sent << " | 速度: "
                             << playback_speed_.load() << "x]" << std::flush;
                    
                    // 重置当前周期统计
                    {
                        std::lock_guard<std::mutex> lock(stats_mutex_);
                        stats_.reset_current_period();
                    }
                }
            }
        });
    }
    
    /**
     * @brief 打印帮助信息
     */
    void print_help() {
        std::cout << "\n=== 控制说明 ===" << std::endl;
        std::cout << "  ↑ 方向键上     - 增加播放速度 (+0.1)" << std::endl;
        std::cout << "  ↓ 方向键下     - 减少播放速度 (-0.1)" << std::endl;
        std::cout << "  P 键          - 暂停/恢复播放" << std::endl;
        std::cout << "  L 键          - 切换循环播放模式" << std::endl;
        std::cout << "  S 键          - 显示详细统计信息" << std::endl;
        std::cout << "  H 键          - 显示此帮助信息" << std::endl;
        std::cout << "  Q 键          - 退出程序" << std::endl;
        std::cout << "=================" << std::endl;
        std::cout << "当前播放速度: " << std::fixed << std::setprecision(1) << playback_speed_.load() << "x (范围: 0.1 - 1.0)" << std::endl;
    }
    
    /**
     * @brief 格式化字节数显示
     * @param bytes 字节数
     * @return 格式化后的字符串
     */
    std::string format_bytes(uint64_t bytes) const {
        const char* units[] = {"B", "KB", "MB", "GB"};
        int unit_index = 0;
        double size = static_cast<double>(bytes);
        
        while (size >= 1024 && unit_index < 3) {
            size /= 1024;
            unit_index++;
        }
        
        std::stringstream ss;
        ss << std::fixed << std::setprecision(2) << size << " " << units[unit_index];
        return ss.str();
    }
};

/**
 * @brief 打印程序使用说明
 * @param program_name 程序名称
 */
void print_usage(const char* program_name) {
    std::cout << "APA .dat文件回放工具" << std::endl;
    std::cout << "用法: " << program_name << " <dat文件目录> [选项]" << std::endl;
    std::cout << "选项:" << std::endl;
    std::cout << "  -s <速度>     播放速度 (默认: 0.5, 范围: 0.1-1.0)" << std::endl;
    std::cout << "  -l            启用循环播放" << std::endl;
    std::cout << "  -h            显示此帮助信息" << std::endl;
    std::cout << std::endl;
    std::cout << "板端连接: ************:5020 (固定)" << std::endl;
    std::cout << std::endl;
    std::cout << "功能特性:" << std::endl;
    std::cout << "  - 实时帧率监控" << std::endl;
    std::cout << "  - 方向键调节播放速度" << std::endl;
    std::cout << "  - 暂停/恢复控制" << std::endl;
    std::cout << "  - 详细统计信息" << std::endl;
    std::cout << "  - 运行时键盘控制" << std::endl;
    std::cout << std::endl;
    std::cout << "控制方式:" << std::endl;
    std::cout << "  ↑/↓ 方向键    调节播放速度" << std::endl;
    std::cout << "  P 键         暂停/恢复" << std::endl;
    std::cout << "  Q 键         退出程序" << std::endl;
    std::cout << std::endl;
    std::cout << "使用示例:" << std::endl;
    std::cout << "  " << program_name << " /path/to/dat/files" << std::endl;
    std::cout << "  " << program_name << " /path/to/dat/files -s 0.8 -l" << std::endl;
}

int main(int argc, char* argv[]) {
    if (argc < 2) {
        print_usage(argv[0]);
        return -1;
    }
    
    std::string dat_directory = argv[1];
    float speed = 0.5f;
    bool loop = false;

    // 解析命令行参数
    for (int i = 2; i < argc; ++i) {
        std::string arg = argv[i];

        if (arg == "-s" && i + 1 < argc) {
            speed = std::atof(argv[++i]);
        } else if (arg == "-l") {
            loop = true;
        } else if (arg == "-h") {
            print_usage(argv[0]);
            return 0;
        }
    }

    // 固定连接到板端 ************:5020
    DatFileSender sender("************", 5020);
    
    // 加载.dat文件
    if (!sender.load_dat_files(dat_directory)) {
        return -1;
    }
    
    // 设置播放参数
    sender.set_playback_speed(speed);
    sender.set_loop_mode(loop);
    
    // 启动控制线程
    sender.start_control_thread();
    
    // 启动发送
    sender.start_sending();
    
    return 0;
}
