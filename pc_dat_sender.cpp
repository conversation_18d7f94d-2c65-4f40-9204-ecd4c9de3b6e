#include <iostream>
#include <fstream>
#include <filesystem>
#include <vector>
#include <string>
#include <thread>
#include <chrono>
#include <algorithm>
#include <atomic>
#include <mutex>
#include <iomanip>
#include <sstream>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <cstring>

#define BUFF_SIZE (1024 * 1024)

/**
 * @brief PC端.dat文件发送程序
 * 位置: tools/dat_playback/pc_dat_sender.cpp
 * 
 * 功能: 读取保存的.dat文件，通过网络发送给板端进行回放
 * 用法: ./pc_dat_sender <dat_files_directory> [options]
 * 
 * 特性:
 * - 支持可调节的发送速率
 * - 实时帧率统计和显示
 * - 循环播放模式
 * - 运行时控制命令
 */

/**
 * @brief 统计信息结构体
 */
struct Statistics {
    std::atomic<uint64_t> total_files_sent{0};
    std::atomic<uint64_t> total_bytes_sent{0};
    std::atomic<uint64_t> files_sent_in_period{0};
    std::chrono::steady_clock::time_point start_time;
    std::chrono::steady_clock::time_point last_reset_time;
    
    Statistics() {
        reset();
    }
    
    void reset() {
        start_time = std::chrono::steady_clock::now();
        last_reset_time = start_time;
        files_sent_in_period = 0;
    }
    
    double get_total_fps() const {
        auto now = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - start_time).count();
        if (duration == 0) return 0.0;
        return (double)total_files_sent * 1000.0 / duration;
    }
    
    double get_current_fps() const {
        auto now = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - last_reset_time).count();
        if (duration == 0) return 0.0;
        return (double)files_sent_in_period * 1000.0 / duration;
    }
    
    void reset_current_period() {
        last_reset_time = std::chrono::steady_clock::now();
        files_sent_in_period = 0;
    }
};

/**
 * @brief .dat文件发送器类
 */
class DatFileSender {
private:
    // 网络配置
    std::string board_ip_;
    int board_port_;
    
    // 播放控制
    std::atomic<float> playback_speed_;
    std::atomic<bool> loop_playback_;
    std::atomic<bool> is_running_;
    std::atomic<bool> is_paused_;
    
    // 文件管理
    std::vector<std::string> dat_files_;
    std::atomic<size_t> current_file_index_;
    
    // 统计信息
    Statistics stats_;
    mutable std::mutex stats_mutex_;
    
    // 控制线程
    std::thread control_thread_;
    std::thread stats_thread_;

public:
    /**
     * @brief 构造函数
     * @param ip 板端IP地址
     * @param port 板端端口
     */
    explicit DatFileSender(const std::string& ip = "************", int port = 5020)
        : board_ip_(ip), board_port_(port), playback_speed_(1.0f), 
          loop_playback_(false), is_running_(false), is_paused_(false),
          current_file_index_(0) {}
    
    /**
     * @brief 析构函数
     */
    ~DatFileSender() {
        stop_sender();
        if (control_thread_.joinable()) {
            control_thread_.join();
        }
        if (stats_thread_.joinable()) {
            stats_thread_.join();
        }
    }
    
    /**
     * @brief 加载目录中的所有.dat文件
     * @param directory 文件目录路径
     * @return 成功返回true，失败返回false
     */
    bool load_dat_files(const std::string& directory) {
        dat_files_.clear();
        
        try {
            for (const auto& entry : std::filesystem::directory_iterator(directory)) {
                if (entry.path().extension() == ".dat") {
                    dat_files_.push_back(entry.path().string());
                }
            }
        } catch (const std::exception& e) {
            std::cerr << "Error reading directory: " << e.what() << std::endl;
            return false;
        }
        
        if (dat_files_.empty()) {
            std::cerr << "No .dat files found in directory: " << directory << std::endl;
            return false;
        }
        
        // 按文件名排序，确保按时间顺序发送
        std::sort(dat_files_.begin(), dat_files_.end());
        
        std::cout << "Loaded " << dat_files_.size() << " .dat files from: " << directory << std::endl;
        for (size_t i = 0; i < dat_files_.size(); ++i) {
            std::cout << "  [" << std::setw(3) << i + 1 << "] " 
                     << std::filesystem::path(dat_files_[i]).filename() << std::endl;
        }
        
        return true;
    }
    
    /**
     * @brief 设置播放速度
     * @param speed 播放速度倍率 (0.1 - 10.0)
     */
    void set_playback_speed(float speed) {
        if (speed >= 0.1f && speed <= 10.0f) {
            playback_speed_ = speed;
            std::cout << "Playback speed set to: " << std::fixed << std::setprecision(1) << speed << "x" << std::endl;
        } else {
            std::cout << "Invalid speed. Range: 0.1 - 10.0" << std::endl;
        }
    }
    
    /**
     * @brief 设置循环播放模式
     * @param loop 是否启用循环播放
     */
    void set_loop_mode(bool loop) {
        loop_playback_ = loop;
        std::cout << "Loop playback: " << (loop ? "enabled" : "disabled") << std::endl;
    }
    
    /**
     * @brief 暂停/恢复播放
     */
    void toggle_pause() {
        is_paused_ = !is_paused_;
        std::cout << "Playback " << (is_paused_ ? "paused" : "resumed") << std::endl;
    }
    
    /**
     * @brief 获取当前统计信息
     */
    void print_statistics() const {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        std::cout << "\n=== Statistics ===" << std::endl;
        std::cout << "Total files sent: " << stats_.total_files_sent << std::endl;
        std::cout << "Total bytes sent: " << format_bytes(stats_.total_bytes_sent) << std::endl;
        std::cout << "Average FPS: " << std::fixed << std::setprecision(2) << stats_.get_total_fps() << std::endl;
        std::cout << "Current FPS: " << std::fixed << std::setprecision(2) << stats_.get_current_fps() << std::endl;
        std::cout << "Current file: " << (current_file_index_ + 1) << "/" << dat_files_.size() << std::endl;
        if (!dat_files_.empty() && current_file_index_ < dat_files_.size()) {
            std::cout << "Current file name: " << std::filesystem::path(dat_files_[current_file_index_]).filename() << std::endl;
        }
        std::cout << "Playback speed: " << playback_speed_.load() << "x" << std::endl;
        std::cout << "=================" << std::endl;
    }
    
    /**
     * @brief 启动发送器
     * @return 成功返回true，失败返回false
     */
    bool start_sending() {
        if (dat_files_.empty()) {
            std::cerr << "No .dat files loaded. Call load_dat_files() first." << std::endl;
            return false;
        }

        is_running_ = true;
        stats_.reset();
        
        // 启动统计线程
        start_statistics_thread();

        while (is_running_) {
            // 连接到板端
            int client_fd = connect_to_board();
            if (client_fd < 0) {
                std::cerr << "Failed to connect to board, retrying in 3 seconds..." << std::endl;
                std::this_thread::sleep_for(std::chrono::seconds(3));
                continue;
            }

            std::cout << "Connected to board: " << board_ip_ << ":" << board_port_ << std::endl;

            // 发送数据
            handle_client(client_fd);

            close(client_fd);
            std::cout << "Disconnected from board" << std::endl;

            // 如果不是循环播放，退出
            if (!loop_playback_) {
                break;
            }

            std::cout << "Waiting 3 seconds before reconnecting..." << std::endl;
            std::this_thread::sleep_for(std::chrono::seconds(3));
        }

        return true;
    }
    
    /**
     * @brief 停止发送器
     */
    void stop_sender() {
        is_running_ = false;
    }
    
    /**
     * @brief 启动控制线程
     */
    void start_control_thread() {
        control_thread_ = std::thread([this]() {
            this->control_loop();
        });
    }
    
private:
    /**
     * @brief 连接到板端
     * @return 成功返回socket描述符，失败返回-1
     */
    int connect_to_board() {
        int client_fd = socket(AF_INET, SOCK_STREAM, 0);
        if (client_fd == -1) {
            std::cerr << "Socket creation failed: " << strerror(errno) << std::endl;
            return -1;
        }

        // 设置socket选项
        int val = 1;
        setsockopt(client_fd, SOL_SOCKET, SO_REUSEADDR, &val, sizeof(val));

        struct sockaddr_in board_addr;
        board_addr.sin_family = AF_INET;
        board_addr.sin_port = htons(board_port_);

        if (inet_pton(AF_INET, board_ip_.c_str(), &board_addr.sin_addr) <= 0) {
            std::cerr << "Invalid board address: " << board_ip_ << std::endl;
            close(client_fd);
            return -1;
        }

        if (connect(client_fd, (struct sockaddr*)&board_addr, sizeof(board_addr)) < 0) {
            std::cerr << "Connect to board failed: " << strerror(errno) << std::endl;
            close(client_fd);
            return -1;
        }

        return client_fd;
    }

    /**
     * @brief 处理客户端连接，发送文件数据
     * @param client_fd 客户端socket描述符
     */
    void handle_client(int client_fd) {
        do {
            std::cout << "Starting playback..." << std::endl;
            
            for (current_file_index_ = 0; current_file_index_ < dat_files_.size() && is_running_; ++current_file_index_) {
                // 检查暂停状态
                while (is_paused_ && is_running_) {
                    std::this_thread::sleep_for(std::chrono::milliseconds(100));
                }
                
                if (!is_running_) break;
                
                const auto& dat_file = dat_files_[current_file_index_];
                
                auto start_time = std::chrono::steady_clock::now();
                
                if (!send_dat_file(client_fd, dat_file)) {
                    std::cout << "Send failed, client may have disconnected" << std::endl;
                    return;
                }
                
                // 更新统计信息
                {
                    std::lock_guard<std::mutex> lock(stats_mutex_);
                    stats_.total_files_sent++;
                    stats_.files_sent_in_period++;
                }
                
                // 控制发送速度
                auto elapsed = std::chrono::steady_clock::now() - start_time;
                auto target_interval = std::chrono::milliseconds(static_cast<int>(100 / playback_speed_.load()));
                
                if (elapsed < target_interval) {
                    std::this_thread::sleep_for(target_interval - elapsed);
                }
            }
            
            if (loop_playback_ && is_running_) {
                std::cout << "Loop playback, restarting..." << std::endl;
            }
            
        } while (loop_playback_ && is_running_);
        
        std::cout << "Playback completed" << std::endl;
    }
    
    /**
     * @brief 发送单个.dat文件
     * @param client_fd 客户端socket描述符
     * @param filepath 文件路径
     * @return 成功返回true，失败返回false
     */
    bool send_dat_file(int client_fd, const std::string& filepath) {
        // 1. 读取.dat文件
        std::ifstream file(filepath, std::ios::binary);
        if (!file.is_open()) {
            std::cerr << "Failed to open file: " << filepath << std::endl;
            return false;
        }
        
        // 获取文件大小
        file.seekg(0, std::ios::end);
        size_t filesize = file.tellg();
        file.seekg(0, std::ios::beg);
        
        // 读取文件数据
        std::vector<char> file_data(filesize);
        file.read(file_data.data(), filesize);
        file.close();
        
        // 2. 发送文件信息 (filename + '\n' + filesize)
        std::string filename = std::filesystem::path(filepath).filename();
        std::string file_info = filename + "\n" + std::to_string(filesize);
        
        if (send(client_fd, file_info.c_str(), file_info.length(), 0) <= 0) {
            std::cerr << "Failed to send file info" << std::endl;
            return false;
        }
        
        // 3. 等待ACK确认
        if (!wait_for_ack(client_fd, "ACK")) {
            std::cerr << "Failed to receive ACK" << std::endl;
            return false;
        }
        
        // 4. 发送文件数据
        size_t total_sent = 0;
        while (total_sent < filesize) {
            size_t chunk_size = std::min(static_cast<size_t>(BUFF_SIZE), filesize - total_sent);
            int sent = send(client_fd, file_data.data() + total_sent, chunk_size, 0);
            
            if (sent <= 0) {
                std::cerr << "Failed to send file data" << std::endl;
                return false;
            }
            
            total_sent += sent;
        }
        
        // 更新字节统计
        {
            std::lock_guard<std::mutex> lock(stats_mutex_);
            stats_.total_bytes_sent += filesize;
        }
        
        // 5. 等待ACK1确认
        if (!wait_for_ack(client_fd, "ACK1")) {
            std::cerr << "Failed to receive ACK1" << std::endl;
            return false;
        }
        
        return true;
    }
    
    /**
     * @brief 等待ACK确认
     * @param client_fd 客户端socket描述符
     * @param expected_ack 期望的ACK字符串
     * @return 成功返回true，失败返回false
     */
    bool wait_for_ack(int client_fd, const std::string& expected_ack) {
        char ack_buffer[10] = {0};
        int received = recv(client_fd, ack_buffer, sizeof(ack_buffer) - 1, 0);
        
        if (received <= 0) {
            return false;
        }
        
        ack_buffer[received] = '\0';
        return std::string(ack_buffer) == expected_ack;
    }
    
    /**
     * @brief 控制命令循环
     */
    void control_loop() {
        std::string command;
        print_help();
        
        while (is_running_ && std::cin >> command) {
            if (command == "speed") {
                float new_speed;
                if (std::cin >> new_speed) {
                    set_playback_speed(new_speed);
                }
            } else if (command == "loop") {
                set_loop_mode(!loop_playback_.load());
            } else if (command == "pause") {
                toggle_pause();
            } else if (command == "stats") {
                print_statistics();
            } else if (command == "help") {
                print_help();
            } else if (command == "quit") {
                stop_sender();
                break;
            } else {
                std::cout << "Unknown command: " << command << std::endl;
                print_help();
            }
            
            if (is_running_) {
                std::cout << "> ";
            }
        }
    }
    
    /**
     * @brief 启动统计线程
     */
    void start_statistics_thread() {
        stats_thread_ = std::thread([this]() {
            while (is_running_) {
                std::this_thread::sleep_for(std::chrono::seconds(5));
                if (is_running_) {
                    std::cout << "\r[FPS: " << std::fixed << std::setprecision(1) 
                             << stats_.get_current_fps() << " | Total: " 
                             << stats_.total_files_sent << " | Speed: " 
                             << playback_speed_.load() << "x]" << std::flush;
                    
                    // 重置当前周期统计
                    {
                        std::lock_guard<std::mutex> lock(stats_mutex_);
                        stats_.reset_current_period();
                    }
                }
            }
        });
    }
    
    /**
     * @brief 打印帮助信息
     */
    void print_help() {
        std::cout << "\n=== Control Commands ===" << std::endl;
        std::cout << "  speed <value>  - Set playback speed (0.1-10.0)" << std::endl;
        std::cout << "  loop           - Toggle loop mode" << std::endl;
        std::cout << "  pause          - Toggle pause/resume" << std::endl;
        std::cout << "  stats          - Show detailed statistics" << std::endl;
        std::cout << "  help           - Show this help" << std::endl;
        std::cout << "  quit           - Stop sender" << std::endl;
        std::cout << "=======================" << std::endl;
        std::cout << "> ";
    }
    
    /**
     * @brief 格式化字节数显示
     * @param bytes 字节数
     * @return 格式化后的字符串
     */
    std::string format_bytes(uint64_t bytes) const {
        const char* units[] = {"B", "KB", "MB", "GB"};
        int unit_index = 0;
        double size = static_cast<double>(bytes);
        
        while (size >= 1024 && unit_index < 3) {
            size /= 1024;
            unit_index++;
        }
        
        std::stringstream ss;
        ss << std::fixed << std::setprecision(2) << size << " " << units[unit_index];
        return ss.str();
    }
};

/**
 * @brief 打印程序使用说明
 * @param program_name 程序名称
 */
void print_usage(const char* program_name) {
    std::cout << "APA .dat File Playback Tool" << std::endl;
    std::cout << "Usage: " << program_name << " <dat_files_directory> [options]" << std::endl;
    std::cout << "Options:" << std::endl;
    std::cout << "  -s <speed>    Playback speed (default: 1.0, range: 0.1-10.0)" << std::endl;
    std::cout << "  -l            Enable loop playback" << std::endl;
    std::cout << "  -h            Show this help" << std::endl;
    std::cout << std::endl;
    std::cout << "Board connection: ************:5020 (fixed)" << std::endl;
    std::cout << std::endl;
    std::cout << "Features:" << std::endl;
    std::cout << "  - Real-time FPS monitoring" << std::endl;
    std::cout << "  - Adjustable playback speed" << std::endl;
    std::cout << "  - Pause/resume control" << std::endl;
    std::cout << "  - Detailed statistics" << std::endl;
    std::cout << "  - Runtime command interface" << std::endl;
    std::cout << std::endl;
    std::cout << "Examples:" << std::endl;
    std::cout << "  " << program_name << " /path/to/dat/files" << std::endl;
    std::cout << "  " << program_name << " /path/to/dat/files -s 1.5 -l" << std::endl;
}

int main(int argc, char* argv[]) {
    if (argc < 2) {
        print_usage(argv[0]);
        return -1;
    }
    
    std::string dat_directory = argv[1];
    float speed = 0.5f;
    bool loop = false;

    // 解析命令行参数
    for (int i = 2; i < argc; ++i) {
        std::string arg = argv[i];

        if (arg == "-s" && i + 1 < argc) {
            speed = std::atof(argv[++i]);
        } else if (arg == "-l") {
            loop = true;
        } else if (arg == "-h") {
            print_usage(argv[0]);
            return 0;
        }
    }

    // 固定连接到板端 ************:5020
    DatFileSender sender("************", 5020);
    
    // 加载.dat文件
    if (!sender.load_dat_files(dat_directory)) {
        return -1;
    }
    
    // 设置播放参数
    sender.set_playback_speed(speed);
    sender.set_loop_mode(loop);
    
    // 启动控制线程
    sender.start_control_thread();
    
    // 启动发送
    sender.start_sending();
    
    return 0;
}
